{"name": "faafo-career-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:real": "jest --config jest.config.simple.js __tests__/real-database.test.ts", "test:setup": "tsx scripts/setup-test-database.ts", "test:verify": "tsx scripts/verify-real-database-testing.ts", "test:all": "./run-tests.sh", "test:edge-cases": "tsx __tests__/edge-cases/run-edge-case-tests.ts", "test:edge-cases:auth": "npx jest __tests__/edge-cases/auth-security.test.ts", "test:edge-cases:api": "npx jest __tests__/edge-cases/api-validation.test.ts", "test:edge-cases:db": "npx jest __tests__/edge-cases/database-operations.test.ts", "test:edge-cases:errors": "npx jest __tests__/edge-cases/error-handling.test.ts", "test:edge-cases:performance": "npx jest __tests__/edge-cases/performance-security.test.ts", "test:edge-cases:validation": "npx jest __tests__/edge-cases/validation-edge-cases.test.ts", "test:coverage": "npx jest --coverage", "test:watch": "npx jest --watch", "test:ci": "npx jest --ci --coverage --watchAll=false", "test-crud": "tsx scripts/test-prisma-crud.ts", "prisma:seed": "tsx prisma/seed.ts", "postinstall": "npx prisma generate", "deploy:validate": "node scripts/validate-production-env.js", "deploy:vercel": "./scripts/deploy-to-vercel.sh", "deploy:preview": "vercel", "deploy:production": "vercel --prod", "build:production": "NODE_ENV=production npm run build", "db:migrate:deploy": "npx prisma migrate deploy", "db:generate": "npx prisma generate", "enterprise:health": "node scripts/enterprise-health-check.js", "enterprise:backup": "node scripts/enterprise-backup.js", "enterprise:monitor": "node scripts/enterprise-monitoring.js", "enterprise:analytics": "node scripts/enterprise-analytics.js", "enterprise:security-scan": "node scripts/enterprise-security-scan.js", "test:load": "node scripts/load-testing.js", "test:security": "node scripts/security-testing.js", "test:smoke": "node scripts/smoke-tests.js", "test:smoke:staging": "STAGING=true node scripts/smoke-tests.js", "test:health": "curl -f http://localhost:3000/api/enterprise/health || exit 1", "test:health:production": "curl -f $PRODUCTION_URL/api/enterprise/health || exit 1", "validate:config": "node scripts/validate-enterprise-config.js", "performance:profile": "node scripts/performance-profiling.js", "performance:benchmark": "node scripts/performance-benchmark.js", "security:audit": "npm audit && node scripts/security-audit.js", "security:scan": "node scripts/security-vulnerability-scan.js", "monitoring:setup": "node scripts/setup-monitoring.js", "monitoring:test": "node scripts/test-monitoring.js", "backup:create": "node scripts/create-backup.js", "backup:restore": "node scripts/restore-backup.js", "backup:verify": "node scripts/verify-backup.js", "analytics:report": "node scripts/generate-analytics-report.js", "analytics:export": "node scripts/export-analytics-data.js", "maintenance:enable": "node scripts/enable-maintenance-mode.js", "maintenance:disable": "node scripts/disable-maintenance-mode.js", "cache:clear": "node scripts/clear-cache.js", "cache:warm": "node scripts/warm-cache.js", "logs:export": "node scripts/export-logs.js", "logs:analyze": "node scripts/analyze-logs.js"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@google/generative-ai": "^0.21.0", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.8.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.0.41", "@react-email/html": "^0.0.11", "@sentry/nextjs": "^8.47.0", "@types/dompurify": "^3.0.5", "@types/node-fetch": "^2.6.12", "@vercel/analytics": "^1.5.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dompurify": "^3.2.4", "express-rate-limit": "^7.4.1", "fuse.js": "^7.1.0", "helmet": "^8.0.0", "ioredis": "^5.4.1", "js-yaml": "^4.1.0", "lru-cache": "^10.4.3", "lucide-react": "^0.511.0", "mammoth": "^1.8.0", "next": "^15.3.3", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "pdf-parse": "^1.1.1", "prisma": "^6.8.2", "react": "^18.2.0", "react-cookie-consent": "^9.0.0", "react-dom": "^18.2.0", "react-hook-form": "^7.56.4", "resend": "^4.5.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-react": "^5.18.2", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "zod": "^3.25.56"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/bcrypt": "^5.0.2", "@types/eslint-plugin-security": "^3.0.0", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/lru-cache": "^7.10.9", "@types/node": "^20", "@types/pdf-parse": "^1.1.4", "@types/react": "^19", "@types/react-dom": "^19", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-react": "^5.0.1", "@types/uuid": "^10.0.0", "babel-jest": "^30.0.0-beta.3", "dotenv": "^16.5.0", "eslint": "^9", "eslint-config-next": "15.3.3", "eslint-plugin-security": "^3.0.1", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.4", "tw-animate-css": "^1.3.2", "typescript": "^5"}}