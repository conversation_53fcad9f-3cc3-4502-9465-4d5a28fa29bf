{"total": 19, "passed": 19, "failed": 0, "errors": [], "details": [{"test": "Schema: User<PERSON><PERSON> enum", "passed": true, "details": "Found roles: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, S<PERSON>ER_ADMIN", "timestamp": "2025-06-13T20:22:03.000Z"}, {"test": "Schema: User.role field", "passed": true, "details": "Role field accessible in User model", "timestamp": "2025-06-13T20:22:05.121Z"}, {"test": "Utils: isUserAdmin function", "passed": true, "details": "Function is defined and exported", "timestamp": "2025-06-13T20:22:05.123Z"}, {"test": "Utils: getUserRole function", "passed": true, "details": "Function is defined and exported", "timestamp": "2025-06-13T20:22:05.123Z"}, {"test": "Utils: requireAdmin function", "passed": true, "details": "Function is defined and exported", "timestamp": "2025-06-13T20:22:05.123Z"}, {"test": "Role Assignment: USER", "passed": true, "details": "User <EMAIL> created with role USER", "timestamp": "2025-06-13T20:22:06.043Z"}, {"test": "Role Assignment: ADMIN", "passed": true, "details": "User <EMAIL> created with role ADMIN", "timestamp": "2025-06-13T20:22:06.615Z"}, {"test": "Role Assignment: SUPER_ADMIN", "passed": true, "details": "User <EMAIL> created with role SUPER_ADMIN", "timestamp": "2025-06-13T20:22:07.271Z"}, {"test": "Role Validation: USER", "passed": true, "details": "User <EMAIL> has role USER, expected USER", "timestamp": "2025-06-13T20:22:07.271Z"}, {"test": "Role Validation: ADMIN", "passed": true, "details": "User <EMAIL> has role ADMIN, expected ADMIN", "timestamp": "2025-06-13T20:22:07.272Z"}, {"test": "Role Validation: SUPER_ADMIN", "passed": true, "details": "User <EMAIL> has role SUPER_ADMIN, expected SUPER_ADMIN", "timestamp": "2025-06-13T20:22:07.272Z"}, {"test": "API Protection: src/app/api/learning-paths/route.ts", "passed": true, "details": "No hardcoded admin checks found", "timestamp": "2025-06-13T20:22:07.274Z"}, {"test": "API Protection: src/app/api/learning-paths/[id]/route.ts", "passed": true, "details": "No hardcoded admin checks found", "timestamp": "2025-06-13T20:22:07.274Z"}, {"test": "API Protection: src/app/api/admin/database/route.ts", "passed": true, "details": "No hardcoded admin checks found", "timestamp": "2025-06-13T20:22:07.274Z"}, {"test": "Middleware: File exists", "passed": true, "details": "Admin middleware file found", "timestamp": "2025-06-13T20:22:07.275Z"}, {"test": "Middleware: Core functions", "passed": true, "details": "withAdminAuth: true, checkAdminStatus: true, requireAdminAccess: true", "timestamp": "2025-06-13T20:22:07.275Z"}, {"test": "Scripts: create-admin.ts exists", "passed": true, "details": "Admin creation script found", "timestamp": "2025-06-13T20:22:07.275Z"}, {"test": "Scripts: npm script entry", "passed": true, "details": "Script: tsx scripts/create-admin.ts", "timestamp": "2025-06-13T20:22:07.276Z"}, {"test": "Security: No hardcoded admin checks", "passed": true, "details": "Found 0 hardcoded admin email checks", "timestamp": "2025-06-13T20:22:07.302Z"}]}