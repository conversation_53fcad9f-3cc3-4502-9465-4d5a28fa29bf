import { NextAuthOptions, User as <PERSON><PERSON><PERSON><PERSON><PERSON>, Session } from "next-auth";
import { JWT } from "next-auth/jwt";
import CredentialsProvider from "next-auth/providers/credentials";
import EmailProvider from "next-auth/providers/email";
import { PrismaAdapter } from "@auth/prisma-adapter";
import bcrypt from "bcryptjs";
import prisma from '@/lib/prisma';
import { sendEmail } from '@/lib/email';
import { VerificationEmail } from '@/emails/VerificationEmail';

// Augment the NextAuthUser type to include id
interface User extends NextAuthUser {
  id: string;
}

// Augment the Session.user type
interface ExtendedSession extends Session {
  user?: User & {
    id: string;
  };
}

// Augment the JWT type to include id, email, and name
interface ExtendedJWT extends JWT {
  id: string;
  email: string;
  name: string;
}

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
  },
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials): Promise<User | null> {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email }
        });

        if (!user) {
          return null;
        }

        // Check if account is locked
        if (user.lockedUntil && user.lockedUntil > new Date()) {
          throw new Error('Account is temporarily locked due to too many failed login attempts. Please try again later.');
        }

        const isPasswordValid = await bcrypt.compare(credentials.password, user.password);

        if (!isPasswordValid) {
          // Increment failed login attempts
          const failedAttempts = user.failedLoginAttempts + 1;
          const maxAttempts = 5;
          const lockoutDuration = 15 * 60 * 1000; // 15 minutes in milliseconds

          if (failedAttempts >= maxAttempts) {
            // Lock the account
            await prisma.user.update({
              where: { id: user.id },
              data: {
                failedLoginAttempts: failedAttempts,
                lockedUntil: new Date(Date.now() + lockoutDuration)
              }
            });
            throw new Error('Account locked due to too many failed login attempts. Please try again in 15 minutes.');
          } else {
            // Update failed attempts count
            await prisma.user.update({
              where: { id: user.id },
              data: {
                failedLoginAttempts: failedAttempts
              }
            });
          }
          return null;
        }

        // Check if email is verified (bypass in development)
        if (!user.emailVerified && process.env.NODE_ENV === 'production') {
          throw new Error('Please verify your email address before signing in. Check your inbox for a verification link.');
        }

        // Reset failed login attempts on successful login
        if (user.failedLoginAttempts > 0 || user.lockedUntil) {
          await prisma.user.update({
            where: { id: user.id },
            data: {
              failedLoginAttempts: 0,
              lockedUntil: null
            }
          });
        }

        return { id: user.id, email: user.email, name: user.name } as User;
      }
    }),
    EmailProvider({
      server: {
        host: process.env.EMAIL_SERVER_HOST,
        port: parseInt(process.env.EMAIL_SERVER_PORT || "587"), // Default to 587 if not set
        auth: {
          user: process.env.EMAIL_SERVER_USER,
          pass: process.env.EMAIL_SERVER_PASSWORD,
        },
      },
      from: process.env.EMAIL_FROM || '<EMAIL>',
      sendVerificationRequest: async ({ identifier: email, url }) => {
        try {
          const user = await prisma.user.findUnique({
            where: { email },
          });
          if (user) {
            await sendEmail({
              to: email,
              subject: "Verify your email for FAAFO Career Platform",
              template: <VerificationEmail username={user.name || email} verificationLink={url} />,
            });
            console.log(`Verification email sent to ${email}`);
          } else {
            console.warn(`Attempted to send verification email to non-existent user via magic link: ${email}`);
          }
        } catch (error) {
          console.error(`Failed to send verification email to ${email}:`, error);
        }
      },
    }),
  ],
  jwt: {
    maxAge: 8 * 60 * 60, // 8 hours (reduced for security)
  },
  callbacks: {
    async jwt({ token, user, trigger, account }): Promise<ExtendedJWT> {
      const now = Math.floor(Date.now() / 1000);

      // Regenerate session ID on login for security
      if (trigger === 'signIn' || trigger === 'signUp') {
        token.sessionId = crypto.randomUUID();
        token.iat = now;
        token.lastActivity = now;
        token.loginCount = (token.loginCount as number || 0) + 1;
        token.lastLoginIP = ''; // Would be set from request in real implementation

        // Track login method for security
        if (account) {
          token.loginMethod = account.provider;
        }
      }

      // Enhanced session timeout with multiple levels
      if (token.iat) {
        const sessionAge = now - (token.iat as number);

        // Force re-authentication after 8 hours for sensitive operations
        if (sessionAge > (8 * 60 * 60)) {
          throw new Error('Session expired - please log in again');
        }

        // Warn about session expiry after 6 hours
        if (sessionAge > (6 * 60 * 60)) {
          token.sessionWarning = true;
        }
      }

      // Enhanced session regeneration with adaptive timing
      if (token.lastActivity) {
        const inactivityTime = now - (token.lastActivity as number);

        // Regenerate session ID more frequently for high-value operations
        const regenerationInterval = (token.loginCount as number || 0) > 10 ? (15 * 60) : (30 * 60); // 15 or 30 minutes

        if (inactivityTime > regenerationInterval) {
          const oldSessionId = token.sessionId;
          token.sessionId = crypto.randomUUID();
          token.lastActivity = now;
          token.regenerationCount = (token.regenerationCount as number || 0) + 1;

          // Log session regeneration for security monitoring
          console.log(`Session regenerated: ${oldSessionId} -> ${token.sessionId}`);
        }

        // Auto-logout after 2 hours of inactivity
        if (inactivityTime > (2 * 60 * 60)) {
          throw new Error('Session expired due to inactivity');
        }
      }

      if (user) {
        token.id = (user as User).id;
        token.email = (user as User).email;
        token.name = (user as User).name;
      }

      // Update last activity timestamp
      token.lastActivity = now;

      return token as ExtendedJWT;
    },
    async session({ session, token }): Promise<ExtendedSession> {
      if (session.user && token.id) {
        (session.user as User).id = token.id as string;
        if (token.email) {
          (session.user as User).email = token.email as string;
        }
        if (token.name) {
          (session.user as User).name = token.name as string;
        }
      }

      // Enhanced session security validation
      const now = Math.floor(Date.now() / 1000);

      // Validate session integrity and freshness
      if (token.lastActivity && (now - (token.lastActivity as number)) > (2 * 60 * 60)) {
        throw new Error('Session expired due to inactivity');
      }

      if (token.iat && (now - (token.iat as number)) > (8 * 60 * 60)) {
        throw new Error('Session expired - please log in again');
      }

      // Add comprehensive session security metadata
      (session as any).sessionId = token.sessionId;
      (session as any).lastActivity = token.lastActivity;
      (session as any).sessionWarning = token.sessionWarning;
      (session as any).regenerationCount = token.regenerationCount || 0;
      (session as any).loginCount = token.loginCount || 0;
      (session as any).loginMethod = token.loginMethod || 'credentials';
      (session as any).isSecure = true;
      (session as any).lastValidated = now;

      return session as ExtendedSession;
    },
  },
  events: {
    async createUser(message) {
      console.log("User created:", message.user.email);
    },
  },
  pages: {
    signIn: '/login',
  }
}; 
// Enhanced session security utilities
export const SessionSecurity = {
  /**
   * Validate session integrity
   */
  validateSession: (session: any): boolean => {
    if (!session || !session.sessionId) return false;
    
    const now = Math.floor(Date.now() / 1000);
    
    // Check session age
    if (session.lastActivity && (now - session.lastActivity) > (2 * 60 * 60)) {
      return false;
    }
    
    // Check session creation time
    if (session.iat && (now - session.iat) > (8 * 60 * 60)) {
      return false;
    }
    
    return true;
  },

  /**
   * Generate secure session ID
   */
  generateSessionId: (): string => {
    return crypto.randomUUID() + '-' + Date.now().toString(36);
  },

  /**
   * Check for session hijacking indicators
   */
  detectHijacking: (session: any, request: any): boolean => {
    // In a real implementation, you would check:
    // - IP address changes
    // - User agent changes
    // - Geolocation changes
    // - Unusual activity patterns
    return false;
  }
};
